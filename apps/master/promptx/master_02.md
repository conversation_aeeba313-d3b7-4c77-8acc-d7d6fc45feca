# Master 节点数据库设计和迁移提示词

你是一个专业的数据库架构师和 PostgreSQL 专家。请帮我设计微博舆情分析系统 Master 节点的数据库架构和迁移系统。

## 项目背景
Master 节点作为分布式微博舆情分析系统的中央管理节点，需要：
- 管理所有爬虫节点的状态和配置
- 统一调度和监控全局任务
- 存储和分析汇聚的舆情数据
- 提供用户权限管理和操作审计
- 支持系统监控和告警管理

## 技术栈要求
- **数据库**: PostgreSQL 15+
- **ORM**: SQLx 0.7+ (支持编译时检查和迁移)
- **连接池**: SQLx Pool
- **迁移工具**: SQLx CLI
- **分区策略**: 时间分区 + 哈希分区
- **索引策略**: B-tree + GIN + 部分索引

## 核心表设计要求

### 1. 节点管理相关表
- **crawler_nodes**: 爬虫节点基本信息和状态
- **node_configurations**: 节点配置信息
- **node_heartbeats**: 节点心跳记录(分区表)

### 2. 任务管理相关表
- **global_tasks**: 全局任务管理(分区表)
- **task_assignments**: 任务分配记录
- **task_execution_logs**: 任务执行日志(分区表)
- **task_dependencies**: 任务依赖关系

### 3. 数据分析相关表
- **posts**: 微博内容数据(继承Node节点设计,分区表)
- **users**: 用户信息(继承Node节点设计)
- **comments**: 评论数据(分区表)
- **sentiment_analysis**: 情感分析结果(分区表)
- **events**: 舆情事件管理
- **topics**: 主题标签管理
- **user_profiles**: 用户画像数据

### 4. 系统管理相关表
- **system_users**: 系统用户管理
- **user_roles**: 用户角色定义
- **user_permissions**: 用户权限管理
- **system_configs**: 系统配置管理
- **audit_logs**: 操作审计日志(分区表)

### 5. 监控告警相关表
- **system_alerts**: 系统告警管理
- **alert_rules**: 告警规则配置
- **notification_channels**: 通知渠道配置
- **statistics_cache**: 统计数据缓存

## 数据库设计原则
1. **性能优化**: 合理使用分区、索引、约束
2. **数据完整性**: 外键约束、检查约束、唯一约束
3. **扩展性**: 支持水平扩展和垂直扩展
4. **维护性**: 清晰的命名规范、完善的注释
5. **安全性**: 敏感数据加密、访问权限控制

## 分区策略设计
### 时间分区表
- **posts**: 按月分区 (published_at)
- **comments**: 按月分区 (created_at)
- **task_execution_logs**: 按周分区 (created_at)
- **audit_logs**: 按月分区 (created_at)
- **node_heartbeats**: 按天分区 (heartbeat_time)

### 哈希分区表
- **sentiment_analysis**: 按post_id哈希分区
- **user_profiles**: 按user_id哈希分区

## 索引策略设计
### 主要索引类型
- **B-tree索引**: 常规查询字段
- **GIN索引**: JSONB字段、数组字段、全文搜索
- **部分索引**: 条件查询优化
- **复合索引**: 多字段组合查询
- **唯一索引**: 业务唯一性约束

### 索引命名规范
- 单字段索引: `idx_{table}_{column}`
- 复合索引: `idx_{table}_{col1}_{col2}`
- 唯一索引: `uk_{table}_{column}`
- 部分索引: `idx_{table}_{column}_partial`

## 迁移文件组织
```
src-tauri/migrations/
├── 001_create_base_tables.sql           # 基础表结构
├── 002_create_node_management.sql       # 节点管理表
├── 003_create_task_management.sql       # 任务管理表
├── 004_create_data_analysis.sql         # 数据分析表
├── 005_create_user_management.sql       # 用户管理表
├── 006_create_monitoring.sql            # 监控告警表
├── 007_create_indexes.sql               # 索引创建
├── 008_create_partitions.sql            # 分区创建
├── 009_create_triggers.sql              # 触发器创建
├── 010_create_functions.sql             # 存储过程/函数
├── 011_insert_initial_data.sql          # 初始数据
└── 012_create_views.sql                 # 视图创建
```

## 数据类型规范
- **ID字段**: BIGSERIAL (主键) / BIGINT (外键)
- **UUID字段**: VARCHAR(50) 或 UUID类型
- **时间字段**: TIMESTAMP WITH TIME ZONE
- **JSON字段**: JSONB (支持索引和查询)
- **数组字段**: TEXT[] (配合GIN索引)
- **枚举字段**: INTEGER (配合注释说明)
- **百分比字段**: DECIMAL(5,2) (0.00-100.00)
- **金额字段**: DECIMAL(10,2)

## 性能优化要求
1. **连接池配置**: 最大连接数20-50
2. **查询优化**: 避免N+1查询，使用JOIN
3. **批量操作**: 使用批量插入和更新
4. **缓存策略**: 热点数据Redis缓存
5. **分页查询**: 使用LIMIT/OFFSET或游标分页
6. **慢查询监控**: 记录执行时间>1s的查询

## 数据安全要求
1. **敏感数据加密**: 密码、Token等使用AES-256加密
2. **访问控制**: 基于角色的数据访问控制
3. **数据脱敏**: 日志中敏感信息脱敏
4. **备份策略**: 每日全量备份+实时增量备份
5. **审计日志**: 记录所有数据变更操作

## SQLx集成要求
1. **编译时检查**: 所有SQL语句编译时验证
2. **类型安全**: 使用强类型结构体映射
3. **迁移管理**: 支持up/down迁移
4. **连接池**: 异步连接池管理
5. **事务支持**: 支持嵌套事务和回滚

请按照以上要求，帮我：
1. 设计完整的数据库表结构(DDL)
2. 创建所有必要的索引和约束
3. 实现分区表和分区管理
4. 编写SQLx迁移文件
5. 提供数据库初始化和种子数据
6. 实现Rust数据模型和Repository层
7. 配置数据库连接和连接池
8. 提供数据库管理和维护脚本

注意：
- 所有表必须有created_at和updated_at字段
- 使用统一的更新时间触发器
- 考虑与Node节点数据库的数据同步
- 支持数据库版本升级和回滚
- 提供完整的错误处理和日志记录
